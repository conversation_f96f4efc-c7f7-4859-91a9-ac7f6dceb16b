using MetadataExtractor;
using MetadataExtractor.Formats.Exif;
using MetadataExtractor.Formats.Jpeg;
using MetadataExtractor.Formats.Tiff;
using Microsoft.Extensions.Logging;
using SixLabors.ImageSharp;
using SixLabors.ImageSharp.Metadata.Profiles.Exif;
using System.Text.Json;
using FFMpegCore;

namespace VidCompressor.Services;

/// <summary>
/// Service for extracting and preserving metadata from photos and videos
/// </summary>
public class MediaMetadataService
{
    private readonly ILogger<MediaMetadataService> _logger;

    public MediaMetadataService(ILogger<MediaMetadataService> logger)
    {
        _logger = logger;
    }

    /// <summary>
    /// Extracts metadata from a photo stream
    /// </summary>
    public Task<PhotoMetadataInfo> ExtractPhotoMetadataAsync(Stream photoStream)
    {
        _logger.LogInformation("Extracting photo metadata");
        
        var metadata = new PhotoMetadataInfo();
        
        try
        {
            // Reset stream position
            photoStream.Position = 0;
            
            // Extract metadata using MetadataExtractor
            var directories = ImageMetadataReader.ReadMetadata(photoStream);
            
            // Extract EXIF data
            var exifSubIfdDirectory = directories.OfType<ExifSubIfdDirectory>().FirstOrDefault();
            var exifIfd0Directory = directories.OfType<ExifIfd0Directory>().FirstOrDefault();
            var gpsDirectory = directories.OfType<GpsDirectory>().FirstOrDefault();
            
            if (exifSubIfdDirectory != null)
            {
                // Camera settings
                if (exifSubIfdDirectory.TryGetDouble(ExifDirectoryBase.TagFocalLength, out var focalLength))
                    metadata.FocalLength = focalLength;

                if (exifSubIfdDirectory.TryGetDouble(ExifDirectoryBase.TagFNumber, out var aperture))
                    metadata.Aperture = aperture;

                if (exifSubIfdDirectory.TryGetInt32(ExifDirectoryBase.TagIsoEquivalent, out var iso))
                    metadata.Iso = iso;

                if (exifSubIfdDirectory.HasTagName(ExifDirectoryBase.TagExposureTime))
                    metadata.ExposureTime = exifSubIfdDirectory.GetDescription(ExifDirectoryBase.TagExposureTime);

                if (exifSubIfdDirectory.TryGetDateTime(ExifDirectoryBase.TagDateTimeOriginal, out var dateTime))
                    metadata.DateTimeOriginal = dateTime;

                // Additional camera settings
                if (exifSubIfdDirectory.TryGetInt32(ExifDirectoryBase.TagExifImageWidth, out var pixelX))
                    metadata.PixelXDimension = pixelX;

                if (exifSubIfdDirectory.TryGetInt32(ExifDirectoryBase.TagExifImageHeight, out var pixelY))
                    metadata.PixelYDimension = pixelY;

                if (exifSubIfdDirectory.TryGetDouble(ExifDirectoryBase.TagExposureBias, out var exposureBias))
                    metadata.ExposureBias = exposureBias;

                if (exifSubIfdDirectory.HasTagName(ExifDirectoryBase.TagMeteringMode))
                    metadata.MeteringMode = exifSubIfdDirectory.GetDescription(ExifDirectoryBase.TagMeteringMode);

                if (exifSubIfdDirectory.HasTagName(ExifDirectoryBase.TagFlash))
                    metadata.Flash = exifSubIfdDirectory.GetDescription(ExifDirectoryBase.TagFlash);

                if (exifSubIfdDirectory.HasTagName(ExifDirectoryBase.TagWhiteBalance))
                    metadata.WhiteBalance = exifSubIfdDirectory.GetDescription(ExifDirectoryBase.TagWhiteBalance);

                if (exifSubIfdDirectory.HasTagName(ExifDirectoryBase.TagSceneCaptureType))
                    metadata.SceneCaptureType = exifSubIfdDirectory.GetDescription(ExifDirectoryBase.TagSceneCaptureType);

                if (exifSubIfdDirectory.HasTagName(ExifDirectoryBase.TagColorSpace))
                    metadata.ColorSpace = exifSubIfdDirectory.GetDescription(ExifDirectoryBase.TagColorSpace);
            }
            
            if (exifIfd0Directory != null)
            {
                // Camera make and model
                if (exifIfd0Directory.HasTagName(ExifDirectoryBase.TagMake))
                    metadata.CameraMake = exifIfd0Directory.GetString(ExifDirectoryBase.TagMake);

                if (exifIfd0Directory.HasTagName(ExifDirectoryBase.TagModel))
                    metadata.CameraModel = exifIfd0Directory.GetString(ExifDirectoryBase.TagModel);

                if (exifIfd0Directory.TryGetInt32(ExifDirectoryBase.TagOrientation, out var orientation))
                    metadata.Orientation = orientation;

                // Additional metadata
                if (exifIfd0Directory.HasTagName(ExifDirectoryBase.TagSoftware))
                    metadata.Software = exifIfd0Directory.GetString(ExifDirectoryBase.TagSoftware);

                if (exifIfd0Directory.HasTagName(ExifDirectoryBase.TagArtist))
                    metadata.Artist = exifIfd0Directory.GetString(ExifDirectoryBase.TagArtist);

                if (exifIfd0Directory.HasTagName(ExifDirectoryBase.TagCopyright))
                    metadata.Copyright = exifIfd0Directory.GetString(ExifDirectoryBase.TagCopyright);

                if (exifIfd0Directory.HasTagName(ExifDirectoryBase.TagImageDescription))
                    metadata.ImageDescription = exifIfd0Directory.GetString(ExifDirectoryBase.TagImageDescription);
            }
            
            if (gpsDirectory != null)
            {
                // GPS location
                var location = gpsDirectory.GetGeoLocation();
                if (location != null)
                {
                    metadata.Latitude = location.Latitude;
                    metadata.Longitude = location.Longitude;
                }
                
                if (gpsDirectory.TryGetDouble(GpsDirectory.TagAltitude, out var altitude))
                    metadata.Altitude = altitude;
            }
            
            // Filename will be set from the job if available

            // Store complete EXIF profile for full preservation
            try
            {
                photoStream.Position = 0;
                using var tempImage = Image.Load(photoStream);
                if (tempImage.Metadata.ExifProfile != null)
                {
                    metadata.CompleteExifProfile = tempImage.Metadata.ExifProfile.ToByteArray();
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to extract complete EXIF profile");
            }

            // Store raw EXIF data for complete preservation
            metadata.RawExifData = SerializeDirectories(directories);

            _logger.LogInformation("Successfully extracted photo metadata: Camera={CameraMake} {CameraModel}, DateTime={DateTime}, ISO={Iso}, Aperture={Aperture}, FocalLength={FocalLength}",
                metadata.CameraMake, metadata.CameraModel, metadata.DateTimeOriginal, metadata.Iso, metadata.Aperture, metadata.FocalLength);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to extract photo metadata, continuing without metadata");
        }

        return Task.FromResult(metadata);
    }

    /// <summary>
    /// Extracts metadata from a video file
    /// </summary>
    public async Task<VideoMetadataInfo> ExtractVideoMetadataAsync(string videoFilePath)
    {
        return await ExtractVideoMetadataFromFileAsync(videoFilePath);
    }

    /// <summary>
    /// Extracts full metadata from a video stream using a temporary file approach
    /// This provides complete metadata extraction while minimizing disk usage
    /// </summary>
    public async Task<VideoMetadataInfo> ExtractVideoMetadataFromStreamAsync(Stream videoStream, string? originalFilename = null)
    {
        _logger.LogInformation("Extracting full video metadata from stream");

        var metadata = new VideoMetadataInfo();
        string? tempFilePath = null;

        try
        {
            // Create a temporary file with proper extension for FFMpegCore
            var extension = GetVideoExtensionFromFilename(originalFilename) ?? ".mp4";
            tempFilePath = Path.GetTempFileName();
            var tempFileWithExtension = Path.ChangeExtension(tempFilePath, extension);

            // Delete the original temp file and use the one with proper extension
            if (File.Exists(tempFilePath))
                File.Delete(tempFilePath);
            tempFilePath = tempFileWithExtension;

            // Copy stream to temp file efficiently
            videoStream.Position = 0;
            await using (var tempFileStream = new FileStream(tempFilePath, FileMode.Create, FileAccess.Write, FileShare.None, bufferSize: 81920)) // 80KB buffer
            {
                await videoStream.CopyToAsync(tempFileStream);
            }

            // Reset stream position for subsequent operations
            videoStream.Position = 0;

            // Extract full metadata using FFMpegCore
            var mediaInfo = await FFProbe.AnalyseAsync(tempFilePath);

            // Populate comprehensive metadata
            metadata.Duration = mediaInfo.Duration;
            metadata.Width = mediaInfo.PrimaryVideoStream?.Width ?? 0;
            metadata.Height = mediaInfo.PrimaryVideoStream?.Height ?? 0;
            metadata.FrameRate = mediaInfo.PrimaryVideoStream?.FrameRate ?? 0;
            metadata.Bitrate = mediaInfo.PrimaryVideoStream?.BitRate ?? 0;
            metadata.CodecName = mediaInfo.PrimaryVideoStream?.CodecName;

            // Store all metadata tags for complete preservation
            metadata.RawMetadata = new Dictionary<string, object>();

            // Extract metadata from all available sources (video stream, format, etc.)
            var allTags = new Dictionary<string, string>();

            // Get tags from primary video stream
            if (mediaInfo.PrimaryVideoStream?.Tags != null)
            {
                foreach (var tag in mediaInfo.PrimaryVideoStream.Tags)
                {
                    allTags[tag.Key] = tag.Value;
                    metadata.RawMetadata[tag.Key] = tag.Value;
                }
            }

            // Get tags from format (container level metadata)
            if (mediaInfo.Format?.Tags != null)
            {
                foreach (var tag in mediaInfo.Format.Tags)
                {
                    allTags[tag.Key] = tag.Value;
                    metadata.RawMetadata[$"format_{tag.Key}"] = tag.Value;
                }
            }

            // Extract creation time from multiple possible sources
            var creationTimeSources = new[] { "creation_time", "date", "DATE", "com.apple.quicktime.creationdate" };
            foreach (var source in creationTimeSources)
            {
                if (allTags.ContainsKey(source) && DateTime.TryParse(allTags[source], out var creationTime))
                {
                    metadata.CreationTime = creationTime;
                    _logger.LogInformation("Found creation time from {Source}: {CreationTime}", source, creationTime);
                    break;
                }
            }

            // Extract location data from multiple possible sources
            var locationSources = new[] { "location", "com.apple.quicktime.location.ISO6709", "GPS" };
            foreach (var source in locationSources)
            {
                if (allTags.ContainsKey(source))
                {
                    metadata.Location = allTags[source];
                    _logger.LogInformation("Found location from {Source}: {Location}", source, metadata.Location);
                    break;
                }
            }

            // Add original filename to metadata
            if (!string.IsNullOrEmpty(originalFilename))
            {
                metadata.RawMetadata["original_filename"] = originalFilename;
            }

            _logger.LogInformation("Successfully extracted full video metadata: Duration={Duration}, Resolution={Width}x{Height}, Codec={CodecName}, Bitrate={Bitrate}",
                metadata.Duration, metadata.Width, metadata.Height, metadata.CodecName, metadata.Bitrate);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to extract video metadata from stream, using defaults");

            // Provide basic defaults
            metadata.CodecName = "unknown";
            metadata.RawMetadata = new Dictionary<string, object>();
            if (!string.IsNullOrEmpty(originalFilename))
            {
                metadata.RawMetadata["original_filename"] = originalFilename;
            }
        }
        finally
        {
            // Clean up temp file
            if (!string.IsNullOrEmpty(tempFilePath) && File.Exists(tempFilePath))
            {
                try
                {
                    File.Delete(tempFilePath);
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Failed to delete temporary file: {TempFilePath}", tempFilePath);
                }
            }
        }

        return metadata;
    }

    private string? GetVideoExtensionFromFilename(string? filename)
    {
        if (string.IsNullOrEmpty(filename))
            return null;

        var extension = Path.GetExtension(filename).ToLowerInvariant();
        return extension switch
        {
            ".mp4" => ".mp4",
            ".mov" => ".mov",
            ".avi" => ".avi",
            ".mkv" => ".mkv",
            ".webm" => ".webm",
            ".m4v" => ".m4v",
            _ => ".mp4" // Default to mp4
        };
    }

    /// <summary>
    /// Extracts metadata from a video file (full analysis)
    /// </summary>
    private async Task<VideoMetadataInfo> ExtractVideoMetadataFromFileAsync(string videoFilePath)
    {
        _logger.LogInformation("Extracting video metadata from {FilePath}", videoFilePath);
        
        var metadata = new VideoMetadataInfo();
        
        try
        {
            var mediaInfo = await FFProbe.AnalyseAsync(videoFilePath);
            
            // Basic video properties
            metadata.Duration = mediaInfo.Duration;
            metadata.Width = mediaInfo.PrimaryVideoStream?.Width ?? 0;
            metadata.Height = mediaInfo.PrimaryVideoStream?.Height ?? 0;
            metadata.FrameRate = mediaInfo.PrimaryVideoStream?.FrameRate ?? 0;
            metadata.Bitrate = mediaInfo.PrimaryVideoStream?.BitRate ?? 0;
            metadata.CodecName = mediaInfo.PrimaryVideoStream?.CodecName;
            
            // Store all metadata tags for complete preservation
            metadata.RawMetadata = new Dictionary<string, object>();

            // Extract metadata from all available sources (video stream, format, etc.)
            var allTags = new Dictionary<string, string>();

            // Get tags from primary video stream
            if (mediaInfo.PrimaryVideoStream?.Tags != null)
            {
                foreach (var tag in mediaInfo.PrimaryVideoStream.Tags)
                {
                    allTags[tag.Key] = tag.Value;
                    metadata.RawMetadata[tag.Key] = tag.Value;
                }
            }

            // Get tags from format (container level metadata)
            if (mediaInfo.Format?.Tags != null)
            {
                foreach (var tag in mediaInfo.Format.Tags)
                {
                    allTags[tag.Key] = tag.Value;
                    metadata.RawMetadata[$"format_{tag.Key}"] = tag.Value;
                }
            }

            // Extract creation time from multiple possible sources
            var creationTimeSources = new[] { "creation_time", "date", "DATE", "com.apple.quicktime.creationdate" };
            foreach (var source in creationTimeSources)
            {
                if (allTags.ContainsKey(source) && DateTime.TryParse(allTags[source], out var creationTime))
                {
                    metadata.CreationTime = creationTime;
                    _logger.LogInformation("Found creation time from {Source}: {CreationTime}", source, creationTime);
                    break;
                }
            }

            // Extract location data from multiple possible sources
            var locationSources = new[] { "location", "com.apple.quicktime.location.ISO6709", "GPS" };
            foreach (var source in locationSources)
            {
                if (allTags.ContainsKey(source))
                {
                    metadata.Location = allTags[source];
                    _logger.LogInformation("Found location from {Source}: {Location}", source, metadata.Location);
                    break;
                }
            }
            
            _logger.LogInformation("Successfully extracted video metadata: Duration={Duration}, Resolution={Width}x{Height}",
                metadata.Duration, metadata.Width, metadata.Height);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to extract video metadata from {FilePath}, continuing without metadata", videoFilePath);
        }
        
        return metadata;
    }

    /// <summary>
    /// Applies metadata to a compressed photo
    /// </summary>
    public async Task ApplyPhotoMetadataAsync(Stream compressedPhotoStream, PhotoMetadataInfo originalMetadata, Stream outputStream)
    {
        _logger.LogInformation("Applying metadata to compressed photo");

        try
        {
            compressedPhotoStream.Position = 0;

            using var image = await Image.LoadAsync(compressedPhotoStream);

            // First try to restore the complete original EXIF profile
            if (originalMetadata.CompleteExifProfile != null && originalMetadata.CompleteExifProfile.Length > 0)
            {
                try
                {
                    var exifProfile = new ExifProfile(originalMetadata.CompleteExifProfile);
                    image.Metadata.ExifProfile = exifProfile;
                    _logger.LogInformation("Successfully restored complete EXIF profile");
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Failed to restore complete EXIF profile, falling back to individual metadata fields");

                    // Fallback: create EXIF profile with individual fields
                    await ApplyIndividualMetadataFields(image, originalMetadata);
                }
            }
            else
            {
                // Create EXIF profile with individual fields
                await ApplyIndividualMetadataFields(image, originalMetadata);
            }

            // Save the image with metadata
            await image.SaveAsJpegAsync(outputStream);

            _logger.LogInformation("Successfully applied metadata to compressed photo");
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to apply metadata to compressed photo, saving without metadata");

            // Fallback: copy the compressed image without metadata
            compressedPhotoStream.Position = 0;
            await compressedPhotoStream.CopyToAsync(outputStream);
        }
    }

    private Task ApplyIndividualMetadataFields(Image image, PhotoMetadataInfo originalMetadata)
    {
        try
        {
            var exifProfile = new ExifProfile();

            // Camera information
            if (originalMetadata.CameraMake != null)
                exifProfile.SetValue(ExifTag.Make, originalMetadata.CameraMake);

            if (originalMetadata.CameraModel != null)
                exifProfile.SetValue(ExifTag.Model, originalMetadata.CameraModel);

            if (originalMetadata.Software != null)
                exifProfile.SetValue(ExifTag.Software, originalMetadata.Software);

            // Date and time
            if (originalMetadata.DateTimeOriginal.HasValue)
                exifProfile.SetValue(ExifTag.DateTimeOriginal, originalMetadata.DateTimeOriginal.Value.ToString("yyyy:MM:dd HH:mm:ss"));

            // Camera settings
            if (originalMetadata.Iso.HasValue)
            {
                try
                {
                    exifProfile.SetValue(ExifTag.ISOSpeedRatings, new ushort[] { (ushort)originalMetadata.Iso.Value });
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Failed to set ISO speed rating");
                }
            }

            if (originalMetadata.Aperture.HasValue)
                exifProfile.SetValue(ExifTag.FNumber, new SixLabors.ImageSharp.Rational((uint)(originalMetadata.Aperture.Value * 100), 100));

            if (originalMetadata.FocalLength.HasValue)
                exifProfile.SetValue(ExifTag.FocalLength, new SixLabors.ImageSharp.Rational((uint)(originalMetadata.FocalLength.Value * 1000), 1000));

            if (originalMetadata.ExposureTime != null)
            {
                // Parse exposure time string and convert to rational
                if (originalMetadata.ExposureTime.Contains("/"))
                {
                    var parts = originalMetadata.ExposureTime.Split('/');
                    if (parts.Length == 2 && uint.TryParse(parts[0], out var numerator) && uint.TryParse(parts[1], out var denominator))
                    {
                        exifProfile.SetValue(ExifTag.ExposureTime, new SixLabors.ImageSharp.Rational(numerator, denominator));
                    }
                }
                else if (double.TryParse(originalMetadata.ExposureTime, out var exposureValue))
                {
                    exifProfile.SetValue(ExifTag.ExposureTime, new SixLabors.ImageSharp.Rational((uint)(exposureValue * 1000), 1000));
                }
            }

            // Image properties
            if (originalMetadata.Orientation.HasValue)
                exifProfile.SetValue(ExifTag.Orientation, (ushort)originalMetadata.Orientation.Value);

            if (originalMetadata.PixelXDimension.HasValue)
                exifProfile.SetValue(ExifTag.PixelXDimension, (uint)originalMetadata.PixelXDimension.Value);

            if (originalMetadata.PixelYDimension.HasValue)
                exifProfile.SetValue(ExifTag.PixelYDimension, (uint)originalMetadata.PixelYDimension.Value);

            // GPS data
            if (originalMetadata.Latitude.HasValue && originalMetadata.Longitude.HasValue)
            {
                try
                {
                    exifProfile.SetValue(ExifTag.GPSLatitude, ConvertToGpsRational(Math.Abs(originalMetadata.Latitude.Value)));
                    exifProfile.SetValue(ExifTag.GPSLatitudeRef, originalMetadata.Latitude.Value >= 0 ? "N" : "S");
                    exifProfile.SetValue(ExifTag.GPSLongitude, ConvertToGpsRational(Math.Abs(originalMetadata.Longitude.Value)));
                    exifProfile.SetValue(ExifTag.GPSLongitudeRef, originalMetadata.Longitude.Value >= 0 ? "E" : "W");
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Failed to set GPS coordinates");
                }
            }

            if (originalMetadata.Altitude.HasValue)
            {
                exifProfile.SetValue(ExifTag.GPSAltitude, new SixLabors.ImageSharp.Rational((uint)(Math.Abs(originalMetadata.Altitude.Value) * 1000), 1000));
                exifProfile.SetValue(ExifTag.GPSAltitudeRef, originalMetadata.Altitude.Value >= 0 ? (byte)0 : (byte)1);
            }

            // Additional metadata
            if (originalMetadata.Artist != null)
                exifProfile.SetValue(ExifTag.Artist, originalMetadata.Artist);

            if (originalMetadata.Copyright != null)
                exifProfile.SetValue(ExifTag.Copyright, originalMetadata.Copyright);

            if (originalMetadata.ImageDescription != null)
                exifProfile.SetValue(ExifTag.ImageDescription, originalMetadata.ImageDescription);

            // Apply the EXIF profile
            image.Metadata.ExifProfile = exifProfile;

            _logger.LogInformation("Applied individual metadata fields to compressed photo");
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to apply individual metadata fields");
        }

        return Task.CompletedTask;
    }

    private SixLabors.ImageSharp.Rational[] ConvertToGpsRational(double coordinate)
    {
        var degrees = (int)coordinate;
        var minutes = (int)((coordinate - degrees) * 60);
        var seconds = ((coordinate - degrees) * 60 - minutes) * 60;

        return new[]
        {
            new SixLabors.ImageSharp.Rational((uint)degrees, 1),
            new SixLabors.ImageSharp.Rational((uint)minutes, 1),
            new SixLabors.ImageSharp.Rational((uint)(seconds * 1000), 1000)
        };
    }

    private bool IsMP4Container(byte[] buffer)
    {
        // Check for MP4 file signature (ftyp box)
        if (buffer.Length >= 8)
        {
            // Look for 'ftyp' at offset 4
            return buffer[4] == 0x66 && buffer[5] == 0x74 && buffer[6] == 0x79 && buffer[7] == 0x70;
        }
        return false;
    }

    private bool IsAVIContainer(byte[] buffer)
    {
        // Check for AVI file signature (RIFF...AVI)
        if (buffer.Length >= 12)
        {
            return buffer[0] == 0x52 && buffer[1] == 0x49 && buffer[2] == 0x46 && buffer[3] == 0x46 && // RIFF
                   buffer[8] == 0x41 && buffer[9] == 0x56 && buffer[10] == 0x49 && buffer[11] == 0x20; // AVI
        }
        return false;
    }

    private bool IsMOVContainer(byte[] buffer)
    {
        // MOV files are similar to MP4, check for QuickTime signatures
        if (buffer.Length >= 8)
        {
            // Look for 'ftyp' with QuickTime brand
            if (buffer[4] == 0x66 && buffer[5] == 0x74 && buffer[6] == 0x79 && buffer[7] == 0x70)
            {
                // Check for QuickTime brand codes in the next 4 bytes
                if (buffer.Length >= 12)
                {
                    return buffer[8] == 0x71 && buffer[9] == 0x74; // 'qt'
                }
            }
        }
        return false;
    }

    private void ExtractBasicMP4Info(byte[] buffer, VideoMetadataInfo metadata)
    {
        try
        {
            // This is a simplified MP4 header parser
            // In a full implementation, you'd parse the MP4 box structure
            // For now, we'll just set some basic defaults
            metadata.RawMetadata = metadata.RawMetadata ?? new Dictionary<string, object>();
            metadata.RawMetadata["container_format"] = "mp4";
            metadata.RawMetadata["header_detected"] = true;
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to extract basic MP4 info from header");
        }
    }

    /// <summary>
    /// Applies metadata to a compressed video using FFmpeg
    /// </summary>
    public async Task ApplyVideoMetadataAsync(string compressedVideoPath, VideoMetadataInfo originalMetadata, string outputPath)
    {
        _logger.LogInformation("Applying metadata to compressed video from {InputPath} to {OutputPath}", compressedVideoPath, outputPath);

        try
        {
            // Build metadata arguments
            var metadataArgs = new List<string>();

            // Add creation time if available
            if (originalMetadata.CreationTime.HasValue)
            {
                var creationTimeStr = originalMetadata.CreationTime.Value.ToString("yyyy-MM-ddTHH:mm:ss.fffZ");
                metadataArgs.Add($"-metadata creation_time={creationTimeStr}");
                _logger.LogInformation("Adding creation_time metadata: {CreationTime}", creationTimeStr);
            }

            // Add location if available
            if (!string.IsNullOrEmpty(originalMetadata.Location))
            {
                metadataArgs.Add($"-metadata location=\"{originalMetadata.Location}\"");
                _logger.LogInformation("Adding location metadata: {Location}", originalMetadata.Location);
            }

            // Add any additional metadata from raw metadata
            if (originalMetadata.RawMetadata != null)
            {
                foreach (var kvp in originalMetadata.RawMetadata)
                {
                    // Skip creation_time and location to avoid duplicates, and skip technical fields that shouldn't be copied
                    if (kvp.Key != "creation_time" &&
                        kvp.Key != "location" &&
                        kvp.Key != "original_filename" &&
                        kvp.Key != "container_format" &&
                        kvp.Key != "header_detected")
                    {
                        metadataArgs.Add($"-metadata {kvp.Key}=\"{kvp.Value}\"");
                        _logger.LogDebug("Adding metadata: {Key}={Value}", kvp.Key, kvp.Value);
                    }
                }
            }

            // Use FFMpegCore with proper stream copying and metadata application
            var metadataArgsString = string.Join(" ", metadataArgs);

            await FFMpegArguments
                .FromFileInput(compressedVideoPath)
                .OutputToFile(outputPath, true, options => options
                    .CopyChannel() // Copy video and audio streams without re-encoding
                    .WithCustomArgument(metadataArgsString)) // Add all metadata
                .ProcessAsynchronously();

            _logger.LogInformation("Successfully applied metadata to compressed video");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to apply metadata to compressed video, using original compressed file");

            // Fallback: copy the compressed video without additional metadata
            if (compressedVideoPath != outputPath)
            {
                try
                {
                    File.Copy(compressedVideoPath, outputPath, true);
                    _logger.LogInformation("Copied original compressed video as fallback");
                }
                catch (Exception copyEx)
                {
                    _logger.LogError(copyEx, "Failed to copy compressed video as fallback");
                    throw;
                }
            }
        }
    }

    private string SerializeDirectories(IEnumerable<MetadataExtractor.Directory> directories)
    {
        var data = new Dictionary<string, Dictionary<string, string>>();
        
        foreach (var directory in directories)
        {
            var directoryData = new Dictionary<string, string>();
            
            foreach (var tag in directory.Tags)
            {
                try
                {
                    directoryData[tag.Name] = tag.Description ?? "";
                }
                catch
                {
                    // Skip problematic tags
                }
            }
            
            if (directoryData.Any())
            {
                data[directory.Name] = directoryData;
            }
        }
        
        return JsonSerializer.Serialize(data);
    }


}

/// <summary>
/// Photo metadata information
/// </summary>
public class PhotoMetadataInfo
{
    public string? CameraMake { get; set; }
    public string? CameraModel { get; set; }
    public DateTime? DateTimeOriginal { get; set; }
    public double? FocalLength { get; set; }
    public double? Aperture { get; set; }
    public int? Iso { get; set; }
    public string? ExposureTime { get; set; }
    public int? Orientation { get; set; }
    public double? Latitude { get; set; }
    public double? Longitude { get; set; }
    public double? Altitude { get; set; }
    public string? RawExifData { get; set; }

    // Additional metadata for better preservation
    public string? OriginalFilename { get; set; }
    public string? Software { get; set; }
    public string? Artist { get; set; }
    public string? Copyright { get; set; }
    public string? ImageDescription { get; set; }
    public int? PixelXDimension { get; set; }
    public int? PixelYDimension { get; set; }
    public string? ColorSpace { get; set; }
    public double? ExposureBias { get; set; }
    public string? MeteringMode { get; set; }
    public string? Flash { get; set; }
    public string? WhiteBalance { get; set; }
    public string? SceneCaptureType { get; set; }

    // Store complete EXIF profile as byte array for full preservation
    public byte[]? CompleteExifProfile { get; set; }
}

/// <summary>
/// Video metadata information
/// </summary>
public class VideoMetadataInfo
{
    public TimeSpan Duration { get; set; }
    public int Width { get; set; }
    public int Height { get; set; }
    public double FrameRate { get; set; }
    public long Bitrate { get; set; }
    public string? CodecName { get; set; }
    public DateTime? CreationTime { get; set; }
    public string? Location { get; set; }
    public Dictionary<string, object>? RawMetadata { get; set; }
}
