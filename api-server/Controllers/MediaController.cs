using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Security.Claims;
using VidCompressor.Models;
using VidCompressor.Services;
using VidCompressor.Repositories;


namespace VidCompressor.ApiServer.Controllers;

[ApiController]
[Route("api/[controller]")]
[Authorize]
public class MediaController : ControllerBase
{
    private readonly ICompressionJobRepository _compressionJobRepository;
    private readonly IUserMediaItemRepository _userMediaItemRepository;
    private readonly CloudTasksService _cloudTasksService;
    private readonly CreditsService _creditsService;
    private readonly GoogleCloudStorageService _storageService;
    private readonly ILogger<MediaController> _logger;

    public MediaController(
        ICompressionJobRepository compressionJobRepository,
        IUserMediaItemRepository userMediaItemRepository,
        CloudTasksService cloudTasksService,
        CreditsService creditsService,
        GoogleCloudStorageService storageService,
        ILogger<MediaController> logger)
    {
        _compressionJobRepository = compressionJobRepository;
        _userMediaItemRepository = userMediaItemRepository;
        _cloudTasksService = cloudTasksService;
        _creditsService = creditsService;
        _storageService = storageService;
        _logger = logger;
    }

    /// <summary>
    /// Initiates media compression (photos or videos) using appropriate compression service
    /// </summary>
    [HttpPost("{mediaItemId}/compress")]
    public async Task<IActionResult> CompressMedia(string mediaItemId, [FromBody] CompressionJobRequest request)
    {
        var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        if (userId == null) return Unauthorized();

        _logger.LogInformation("Received compression request for media item {MediaItemId}, MediaType: {MediaType}, Quality: {Quality}, Filename: {Filename}",
            mediaItemId, request?.MediaType, request?.Quality, request?.Filename);

        if (request == null)
        {
            return BadRequest(new { error = "Request body is required" });
        }

        try
        {
            // Calculate credit cost for this operation
            double? durationMinutes = null;
            if (request.MediaType == MediaType.Video)
            {
                // Default to 1 minute for videos since duration parsing from metadata is complex
                durationMinutes = 1.0;
                _logger.LogInformation("Using default 1 minute duration for video {MediaItemId} credit calculation", mediaItemId);
            }

            var creditCost = await _creditsService.CalculateCreditCostAsync(
                request.MediaType,
                request.Quality,
                durationMinutes);

            // Check if user has sufficient credits
            var hasSufficientCredits = await _creditsService.HasSufficientCreditsAsync(userId, creditCost);
            if (!hasSufficientCredits)
            {
                var currentBalance = await _creditsService.GetUserCreditsAsync(userId);
                return BadRequest(new {
                    error = "Insufficient credits",
                    required = creditCost,
                    current = currentBalance,
                    shortfall = creditCost - currentBalance
                });
            }

            // Get creation time from database if available
            DateTime? originalCreationTime = request.OriginalCreationTime;
            if (!originalCreationTime.HasValue)
            {
                try
                {
                    var userMediaItem = await _userMediaItemRepository.GetByUserAndGoogleMediaItemIdAsync(userId, mediaItemId);
                    originalCreationTime = userMediaItem?.CreationTime;
                    if (originalCreationTime.HasValue)
                    {
                        _logger.LogInformation("Found creation time from database for media item {MediaItemId}: {CreationTime}", mediaItemId, originalCreationTime);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Failed to get creation time from database for media item {MediaItemId}", mediaItemId);
                }
            }

            // Create compression job record
            var compressionJob = new CompressionJob
            {
                UserId = userId,
                MediaItemId = mediaItemId,
                MediaType = request.MediaType,
                Quality = request.Quality,
                UploadToGooglePhotos = request.UploadToGooglePhotos,
                BaseUrl = request.BaseUrl,
                OriginalWidth = request.OriginalWidth,
                OriginalHeight = request.OriginalHeight,
                OriginalFilename = request.Filename,
                GooglePhotosUrl = request.GooglePhotosUrl,
                OriginalCreationTime = originalCreationTime,
                CreditsUsed = creditCost,
                Status = CompressionJobStatus.Queued
            };

            await _compressionJobRepository.AddAsync(compressionJob);

            // Deduct credits from user account
            var mediaTypeText = request.MediaType == MediaType.Photo ? "photo" : "video";
            var deductionSuccess = await _creditsService.DeductCreditsAsync(
                userId,
                creditCost,
                $"{char.ToUpper(mediaTypeText[0])}{mediaTypeText[1..]} compression ({request.Quality} quality)",
                compressionJob.Id);

            if (!deductionSuccess)
            {
                // If credit deduction fails, mark job as failed and don't process
                compressionJob.Status = CompressionJobStatus.Failed;
                compressionJob.ErrorMessage = "Failed to deduct credits";
                await _compressionJobRepository.UpdateAsync(compressionJob);

                return BadRequest(new { error = "Failed to deduct credits for compression" });
            }

            // Job creation automatically triggers Firestore real-time updates via CompressionJobRepository
            _logger.LogInformation("Created compression job {JobId} for user {UserId} - Firestore will handle real-time updates", compressionJob.Id, userId);

            // Queue the job for background processing
            await _cloudTasksService.EnqueueCompressionJobAsync(compressionJob.Id);

            return Ok(new CompressionJobResponse
            {
                JobId = compressionJob.Id,
                Status = compressionJob.Status.ToString(),
                Message = $"{char.ToUpper(mediaTypeText[0])}{mediaTypeText[1..]} compression job has been queued",
                CreatedAt = compressionJob.CreatedAt
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to queue compression job for media item {MediaItemId}", mediaItemId);
            return StatusCode(500, new { error = "Failed to queue compression job", details = ex.Message });
        }
    }

    /// <summary>
    /// Gets the status of a compression job
    /// </summary>
    [HttpGet("jobs/{jobId}/status")]
    public async Task<IActionResult> GetCompressionJobStatus(string jobId)
    {
        var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        if (userId == null) return Unauthorized();

        var job = await _compressionJobRepository.GetByIdAsync(jobId);

        if (job != null && job.UserId != userId)
        {
            job = null; // User doesn't own this job
        }

        if (job == null)
        {
            return NotFound(new { message = "Compression job not found" });
        }

        return Ok(new CompressionJobResponse
        {
            JobId = job.Id,
            Status = job.Status.ToString(),
            Message = GetStatusMessage(job.Status),
            CreatedAt = job.CreatedAt,
            CompletedAt = job.CompletedAt,
            CompressionRatio = job.CompressionRatio,
            ErrorMessage = job.ErrorMessage
        });
    }

    /// <summary>
    /// Gets all compression jobs for the current user
    /// </summary>
    [HttpGet("jobs")]
    public async Task<IActionResult> GetCompressionJobs()
    {
        var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        if (userId == null) return Unauthorized();

        var jobs = await _compressionJobRepository.GetByUserIdAsync(userId, 50);

        var jobList = jobs.Select(j => new
        {
            jobId = j.Id,
            mediaItemId = j.MediaItemId,
            filename = j.OriginalFilename ?? $"{(j.MediaType == MediaType.Video ? "Video" : "Photo")}_{j.MediaItemId.Substring(Math.Max(0, j.MediaItemId.Length - 8))}",
            mimeType = j.MediaType == MediaType.Video ? "video/mp4" : "image/jpeg",
            baseUrl = j.BaseUrl,
            status = j.Status.ToString(),
            message = GetStatusMessage(j.Status),
            progress = GetProgressPercentage(j.Status),
            quality = j.Quality,
            uploadToGooglePhotos = j.UploadToGooglePhotos,
            createdAt = j.CreatedAt.ToString("yyyy-MM-ddTHH:mm:ss.fffZ"),
            completedAt = j.CompletedAt.HasValue ? j.CompletedAt.Value.ToString("yyyy-MM-ddTHH:mm:ss.fffZ") : null,
            compressionRatio = j.CompressionRatio,
            error = j.ErrorMessage,
            mediaType = j.MediaType == MediaType.Video ? "Video" : "Photo",
            googlePhotosUrl = j.GooglePhotosUrl,
            compressedGooglePhotosUrl = j.CompressedGooglePhotosUrl,
            creditsUsed = j.CreditsUsed
        }).ToList();

        return Ok(jobList);
    }

    /// <summary>
    /// Deletes a compression job
    /// </summary>
    [HttpDelete("jobs/{jobId}")]
    public async Task<IActionResult> DeleteCompressionJob(string jobId)
    {
        var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        if (userId == null) return Unauthorized();

        var job = await _compressionJobRepository.GetByIdAsync(jobId);

        if (job == null || job.UserId != userId)
        {
            return NotFound(new { message = "Compression job not found" });
        }



        // Only allow deletion of completed, failed, or cancelled jobs
        if (job.Status == CompressionJobStatus.DownloadingFromGooglePhotos ||
            job.Status == CompressionJobStatus.UploadingToStorage ||
            job.Status == CompressionJobStatus.TranscodingInProgress ||
            job.Status == CompressionJobStatus.DownloadingFromStorage ||
            job.Status == CompressionJobStatus.UploadingToGooglePhotos)
        {
            return BadRequest(new { message = "Cannot delete an active job. Please wait for it to complete or cancel it first." });
        }

        // Clean up associated files before deleting the job
        await CleanupJobFilesAsync(job);

        await _compressionJobRepository.DeleteAsync(job.Id);

        // Job deletion automatically triggers Firestore real-time updates via CompressionJobRepository
        _logger.LogInformation("Deleted compression job {JobId} for user {UserId} - Firestore will handle real-time updates", jobId, userId);

        return Ok(new { message = "Compression job deleted successfully" });
    }

    /// <summary>
    /// Deletes all completed, failed, or cancelled compression jobs for the current user
    /// </summary>
    [HttpDelete("jobs")]
    public async Task<IActionResult> DeleteAllCompressionJobs()
    {
        var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        if (userId == null) return Unauthorized();

        var deletableJobs = await _compressionJobRepository.GetDeletableJobsByUserIdAsync(userId);

        if (!deletableJobs.Any())
        {
            return Ok(new { message = "No jobs to delete", deletedCount = 0 });
        }

        // Clean up associated files for all jobs before deleting them
        foreach (var job in deletableJobs)
        {
            await CleanupJobFilesAsync(job);
        }

        var jobIds = deletableJobs.Select(j => j.Id).ToList();
        await _compressionJobRepository.DeleteMultipleAsync(jobIds);

        // Bulk job deletion automatically triggers Firestore real-time updates via CompressionJobRepository
        _logger.LogInformation("Deleted {Count} compression jobs for user {UserId} - Firestore will handle real-time updates", jobIds.Count, userId);

        return Ok(new { message = $"Deleted {jobIds.Count} compression jobs", deletedCount = jobIds.Count });
    }

    /// <summary>
    /// Downloads the compressed file for a completed compression job
    /// </summary>
    [HttpGet("jobs/{jobId}/download")]
    public async Task<IActionResult> DownloadCompressedFile(string jobId)
    {
        var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        if (userId == null) return Unauthorized();

        var job = await _compressionJobRepository.GetByIdAsync(jobId);

        if (job == null || job.UserId != userId)
        {
            return NotFound(new { message = "Compression job not found" });
        }



        if (job.Status != CompressionJobStatus.Completed)
        {
            return BadRequest(new { message = "Job is not completed yet" });
        }

        try
        {
            // Try cloud storage first (for both photos and videos)
            if (!string.IsNullOrEmpty(job.OutputStoragePath))
            {
                var storageService = HttpContext.RequestServices.GetRequiredService<GoogleCloudStorageService>();

                try
                {
                    using var compressedStream = await storageService.DownloadFromOutputBucketAsync(job.OutputStoragePath);
                    var memoryStream = new MemoryStream();
                    await compressedStream.CopyToAsync(memoryStream);
                    memoryStream.Position = 0;

                    var fileName = job.OriginalFilename;
                    string mimeType;

                    if (job.MediaType == MediaType.Photo)
                    {
                        var extension = job.Quality.ToLower() == "low" ? ".webp" : ".jpg";
                        if (string.IsNullOrEmpty(fileName))
                        {
                            // Create unique filename with timestamp and short media ID
                            var timestamp = job.CompletedAt?.ToString("yyyyMMdd_HHmmss") ?? DateTime.UtcNow.ToString("yyyyMMdd_HHmmss");
                            var shortMediaId = job.MediaItemId.Length > 8 ? job.MediaItemId[..8] : job.MediaItemId;
                            fileName = $"compressed_{job.Quality}_{timestamp}_{shortMediaId}{extension}";
                        }
                        else if (!fileName.EndsWith(extension))
                        {
                            fileName = Path.GetFileNameWithoutExtension(fileName) + extension;
                        }
                        mimeType = GetMimeType(extension);
                    }
                    else
                    {
                        if (string.IsNullOrEmpty(fileName))
                        {
                            // Create unique filename with timestamp and short media ID
                            var timestamp = job.CompletedAt?.ToString("yyyyMMdd_HHmmss") ?? DateTime.UtcNow.ToString("yyyyMMdd_HHmmss");
                            var shortMediaId = job.MediaItemId.Length > 8 ? job.MediaItemId[..8] : job.MediaItemId;
                            fileName = $"compressed_{job.Quality}_{timestamp}_{shortMediaId}.mp4";
                        }
                        else if (!fileName.EndsWith(".mp4"))
                        {
                            fileName = Path.GetFileNameWithoutExtension(fileName) + ".mp4";
                        }
                        mimeType = "video/mp4";
                    }

                    return File(memoryStream.ToArray(), mimeType, fileName);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Failed to download compressed file from cloud storage for job {JobId}", jobId);

                    // For photos, fallback to local file if cloud storage fails
                    if (job.MediaType == MediaType.Photo && !string.IsNullOrEmpty(job.CompressedFilePath))
                    {
                        _logger.LogInformation("Falling back to local file for photo job {JobId}", jobId);
                        // Continue to local file fallback below
                    }
                    else
                    {
                        return StatusCode(500, new { message = "Failed to download compressed file from cloud storage" });
                    }
                }
            }

            // Fallback: For photos, check if we have a local compressed file
            if (job.MediaType == MediaType.Photo && !string.IsNullOrEmpty(job.CompressedFilePath))
            {
                if (System.IO.File.Exists(job.CompressedFilePath))
                {
                    var fileBytes = await System.IO.File.ReadAllBytesAsync(job.CompressedFilePath);
                    var fileName = job.OriginalFilename;
                    var extension = string.Equals(job.Quality, "low", StringComparison.OrdinalIgnoreCase) ? ".webp" : ".jpg";

                    if (string.IsNullOrEmpty(fileName))
                    {
                        // Create unique filename with timestamp and short media ID
                        var timestamp = job.CompletedAt?.ToString("yyyyMMdd_HHmmss") ?? DateTime.UtcNow.ToString("yyyyMMdd_HHmmss");
                        var shortMediaId = job.MediaItemId.Length > 8 ? job.MediaItemId[..8] : job.MediaItemId;
                        fileName = $"compressed_{job.Quality}_{timestamp}_{shortMediaId}{extension}";
                    }
                    else if (!fileName.EndsWith(extension))
                    {
                        fileName = Path.GetFileNameWithoutExtension(fileName) + extension;
                    }

                    return File(fileBytes, GetMimeType(extension), fileName);
                }
            }

            return NotFound(new { message = "Compressed file not found" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error downloading compressed file for job {JobId}", jobId);
            return StatusCode(500, new { message = "Error downloading compressed file" });
        }
    }

    /// <summary>
    /// Manually trigger cleanup of old compressed files (admin endpoint)
    /// </summary>
    [HttpPost("cleanup")]
    public async Task<IActionResult> CleanupOldFiles()
    {
        var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        if (userId == null) return Unauthorized();

        try
        {
            var cutoffDate = DateTime.UtcNow.AddDays(-7); // Files older than 7 days

            var oldJobs = await _compressionJobRepository.GetOldJobsByUserIdAsync(userId, cutoffDate);

            var cleanedCount = 0;
            var storageService = HttpContext.RequestServices.GetRequiredService<GoogleCloudStorageService>();
            var jobsToUpdate = new List<CompressionJob>();

            foreach (var job in oldJobs)
            {
                var jobUpdated = false;

                // Clean up local files
                if (!string.IsNullOrEmpty(job.CompressedFilePath) && System.IO.File.Exists(job.CompressedFilePath))
                {
                    try
                    {
                        System.IO.File.Delete(job.CompressedFilePath);
                        job.CompressedFilePath = null;
                        jobUpdated = true;
                        cleanedCount++;
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, "Failed to delete file {FilePath}", job.CompressedFilePath);
                    }
                }

                // Clean up storage files
                if (!string.IsNullOrEmpty(job.OutputStoragePath))
                {
                    try
                    {
                        await storageService.DeleteFileAsync(job.OutputStoragePath);
                        job.OutputStoragePath = null;
                        jobUpdated = true;
                        cleanedCount++;
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, "Failed to delete storage file {StoragePath}", job.OutputStoragePath);
                    }
                }

                if (jobUpdated)
                {
                    jobsToUpdate.Add(job);
                }
            }

            // Update all modified jobs
            foreach (var job in jobsToUpdate)
            {
                await _compressionJobRepository.UpdateAsync(job);
            }

            return Ok(new { message = $"Cleaned up {cleanedCount} old files", jobsProcessed = oldJobs.Count() });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during manual cleanup");
            return StatusCode(500, new { message = "Error during cleanup" });
        }
    }

    /// <summary>
    /// Serves cached preview images from cloud storage
    /// </summary>
    [HttpGet("jobs/{jobId}/preview")]
    [AllowAnonymous]
    public async Task<IActionResult> GetCachedPreview(string jobId)
    {
        try
        {
            _logger.LogInformation("Serving cached preview for job {JobId}", jobId);

            // Get the job to find the preview image path
            var job = await _compressionJobRepository.GetByIdAsync(jobId);
            if (job == null)
            {
                _logger.LogWarning("Job not found for preview request: {JobId}", jobId);
                return NotFound(new { message = "Job not found" });
            }

            if (string.IsNullOrEmpty(job.PreviewImagePath))
            {
                _logger.LogWarning("No preview image path found for job {JobId}", jobId);
                return NotFound(new { message = "Preview image not available" });
            }

            // Download the preview image from cloud storage
            var imageStream = await _storageService.DownloadFromOutputBucketAsync(job.PreviewImagePath);

            // Determine content type from file extension
            var contentType = GetContentTypeFromPath(job.PreviewImagePath);

            _logger.LogDebug("Serving preview for job {JobId} from {PreviewPath}", jobId, job.PreviewImagePath);

            return File(imageStream, contentType);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to serve cached preview for job {JobId}", jobId);
            return StatusCode(500, new { message = "Failed to retrieve preview image" });
        }
    }

    private async Task CleanupJobFilesAsync(CompressionJob job)
    {
        try
        {
            _logger.LogInformation("Starting cleanup for job {JobId} - OutputPath: {OutputPath}, PreviewPath: {PreviewPath}, CompressedPath: {CompressedPath}",
                job.Id, job.OutputStoragePath ?? "null", job.PreviewImagePath ?? "null", job.CompressedFilePath ?? "null");

            // Clean up output storage files
            if (!string.IsNullOrEmpty(job.OutputStoragePath))
            {
                try
                {
                    await _storageService.DeleteFileAsync(job.OutputStoragePath);
                    _logger.LogInformation("Successfully deleted output storage file for job {JobId}: {OutputPath}", job.Id, job.OutputStoragePath);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Failed to delete output storage file {OutputPath} for job {JobId}", job.OutputStoragePath, job.Id);
                }
            }
            else
            {
                _logger.LogDebug("No output storage path to clean up for job {JobId}", job.Id);
            }

            // Clean up preview images
            if (!string.IsNullOrEmpty(job.PreviewImagePath))
            {
                try
                {
                    await _storageService.DeleteFileAsync(job.PreviewImagePath);
                    _logger.LogInformation("Successfully deleted preview image for job {JobId}: {PreviewPath}", job.Id, job.PreviewImagePath);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Failed to delete preview image {PreviewPath} for job {JobId}", job.PreviewImagePath, job.Id);
                }
            }
            else
            {
                _logger.LogDebug("No preview image path to clean up for job {JobId}", job.Id);
            }

            // Clean up local compressed files
            if (!string.IsNullOrEmpty(job.CompressedFilePath) && System.IO.File.Exists(job.CompressedFilePath))
            {
                try
                {
                    System.IO.File.Delete(job.CompressedFilePath);
                    _logger.LogDebug("Deleted local compressed file for job {JobId}: {CompressedPath}", job.Id, job.CompressedFilePath);
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Failed to delete local compressed file {CompressedPath} for job {JobId}", job.CompressedFilePath, job.Id);
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during file cleanup for job {JobId}", job.Id);
        }
    }

    private static string GetContentTypeFromPath(string filePath)
    {
        var extension = Path.GetExtension(filePath);
        return extension.ToLower() switch
        {
            ".jpg" or ".jpeg" => "image/jpeg",
            ".png" => "image/png",
            ".webp" => "image/webp",
            ".gif" => "image/gif",
            _ => "image/jpeg" // Default to jpeg
        };
    }

    private static string GetMimeType(string extension)
    {
        return extension.ToLower() switch
        {
            ".jpg" or ".jpeg" => "image/jpeg",
            ".webp" => "image/webp",
            ".mp4" => "video/mp4",
            _ => "application/octet-stream"
        };
    }

    private static string GetStatusMessage(CompressionJobStatus status)
    {
        return status switch
        {
            CompressionJobStatus.Queued => "Queued for processing",
            CompressionJobStatus.DownloadingFromGooglePhotos => "Downloading from Google Photos",
            CompressionJobStatus.UploadingToStorage => "Uploading to cloud storage",
            CompressionJobStatus.TranscodingInProgress => "Video transcoding in progress",
            CompressionJobStatus.CompressingImage => "Image compression in progress",
            CompressionJobStatus.DownloadingFromStorage => "Downloading compressed media",
            CompressionJobStatus.ReadyForBatchUpload => "Ready for upload to Google Photos",
            CompressionJobStatus.UploadingToGooglePhotos => "Uploading to Google Photos",
            CompressionJobStatus.Completed => "Compression completed",
            CompressionJobStatus.Failed => "Compression failed",
            CompressionJobStatus.Cancelled => "Compression was cancelled",
            _ => "Unknown status"
        };
    }

    private static int GetProgressPercentage(CompressionJobStatus status)
    {
        return status switch
        {
            CompressionJobStatus.Queued => 0,
            CompressionJobStatus.DownloadingFromGooglePhotos => 10,
            CompressionJobStatus.UploadingToStorage => 20,
            CompressionJobStatus.TranscodingInProgress => 50,
            CompressionJobStatus.CompressingImage => 50,
            CompressionJobStatus.DownloadingFromStorage => 80,
            CompressionJobStatus.ReadyForBatchUpload => 90,
            CompressionJobStatus.UploadingToGooglePhotos => 95,
            CompressionJobStatus.Completed => 100,
            CompressionJobStatus.Failed => 0,
            CompressionJobStatus.Cancelled => 0,
            _ => 0
        };
    }
}
