using VidCompressor.Models;
using VidCompressor.Services;
using VidCompressor.Repositories;
using System.Diagnostics;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using System.Text.Json;

namespace VidCompressor.WorkerService.Services;

public class CompressionBackgroundService : BackgroundService
{
    private readonly IServiceProvider _serviceProvider;
    private readonly ILogger<CompressionBackgroundService> _logger;
    private static readonly ActivitySource Activity = new ActivitySource("VidCompressor.CompressionService");

    public CompressionBackgroundService(
        IServiceProvider serviceProvider,
        ILogger<CompressionBackgroundService> logger,
        IConfiguration configuration)
    {
        _serviceProvider = serviceProvider;
        _logger = logger;
    }

    public override async Task StartAsync(CancellationToken cancellationToken)
    {
        _logger.LogInformation("Compression background service starting");
        await base.StartAsync(cancellationToken);
    }

    public override async Task StopAsync(CancellationToken cancellationToken)
    {
        await base.StopAsync(cancellationToken);
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        // Monitor running transcoder jobs
        while (!stoppingToken.IsCancellationRequested)
        {
            try
            {
                await MonitorTranscoderJobsAsync(stoppingToken);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in transcoder job monitoring");
            }

            await Task.Delay(TimeSpan.FromSeconds(30), stoppingToken); // Check every 30 seconds
        }
    }

    public async Task ProcessCompressionJobAsync(string jobId, CancellationToken cancellationToken = default)
    {
        using var activity = Activity.StartActivity("ProcessCompressionJob");

        _logger.LogInformation("Processing compression job: {JobId}", jobId);
        activity?.SetTag("job.id", jobId);

        try
        {
            await ProcessCompressionJobInternalAsync(jobId, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to process compression job: {JobId}", jobId);
            await MarkJobAsFailed(jobId, ex.Message);
        }
    }

    private async Task ProcessCompressionJobInternalAsync(string jobId, CancellationToken cancellationToken)
    {
        using var scope = _serviceProvider.CreateScope();
        var compressionJobRepository = scope.ServiceProvider.GetRequiredService<ICompressionJobRepository>();
        var userRepository = scope.ServiceProvider.GetRequiredService<IUserRepository>();
        var storageService = scope.ServiceProvider.GetRequiredService<GoogleCloudStorageService>();
        var transcoderService = scope.ServiceProvider.GetRequiredService<GoogleTranscoderService>();
        var googlePhotosService = scope.ServiceProvider.GetRequiredService<GooglePhotosService>();
        var imageCompressionService = scope.ServiceProvider.GetRequiredService<ImageCompressionService>();
        var metadataService = scope.ServiceProvider.GetRequiredService<MediaMetadataService>();
        var previewCacheService = scope.ServiceProvider.GetRequiredService<PreviewCacheService>();

        var job = await compressionJobRepository.GetByIdAsync(jobId);
        if (job == null)
        {
            _logger.LogWarning("Compression job not found: {JobId}", jobId);
            return;
        }

        try
        {
            var accessToken = await GetUserAccessToken(userRepository, job.UserId);
            if (string.IsNullOrEmpty(accessToken))
            {
                throw new InvalidOperationException("Unable to get valid access token for user");
            }

            // Cache preview image early in the process (before downloading media)
            await CachePreviewImageAsync(compressionJobRepository, job, previewCacheService, accessToken);

            if (job.MediaType == MediaType.Video)
            {
                await ProcessVideoCompressionAsync(compressionJobRepository, job, storageService, transcoderService, googlePhotosService, metadataService, accessToken, cancellationToken);
            }
            else if (job.MediaType == MediaType.Photo)
            {
                await ProcessPhotoCompressionAsync(compressionJobRepository, job, storageService, imageCompressionService, googlePhotosService, metadataService, accessToken, cancellationToken);
            }
            else
            {
                throw new InvalidOperationException($"Unsupported media type: {job.MediaType}");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to start compression job: {JobId}", jobId);
            await MarkJobAsFailed(jobId, ex.Message);
        }
    }

    private async Task ProcessVideoCompressionAsync(
        ICompressionJobRepository compressionJobRepository,
        CompressionJob job,
        GoogleCloudStorageService storageService,
        GoogleTranscoderService transcoderService,
        GooglePhotosService googlePhotosService,
        MediaMetadataService metadataService,
        string accessToken,
        CancellationToken cancellationToken)
    {
        // Step 1: Download video from Google Photos
        await UpdateJobStatus(compressionJobRepository, job, CompressionJobStatus.DownloadingFromGooglePhotos);

        // Video dimensions should already be set from the PhotosPicker metadata
        if (!job.OriginalWidth.HasValue || !job.OriginalHeight.HasValue)
        {
            _logger.LogWarning("Video dimensions not available for job {JobId}, using defaults", job.Id);
        }

        using var videoStream = await googlePhotosService.DownloadVideoAsync(accessToken, job.MediaItemId, job.BaseUrl);
        var originalSize = videoStream.Length;
        job.OriginalSizeBytes = originalSize;

        // Step 1.5: Create metadata using PhotosPicker creation time and extracted technical metadata
        try
        {
            // Extract technical metadata from video stream (duration, dimensions, etc.)
            var extractedMetadata = await metadataService.ExtractVideoMetadataFromStreamAsync(videoStream, job.OriginalFilename);

            // Create combined metadata using PhotosPicker creation time if available
            var originalMetadata = new VideoMetadataInfo
            {
                Duration = extractedMetadata.Duration,
                Width = extractedMetadata.Width,
                Height = extractedMetadata.Height,
                FrameRate = extractedMetadata.FrameRate,
                Bitrate = extractedMetadata.Bitrate,
                CodecName = extractedMetadata.CodecName,
                CreationTime = job.OriginalCreationTime ?? extractedMetadata.CreationTime, // Prefer PhotosPicker time
                Location = extractedMetadata.Location,
                RawMetadata = extractedMetadata.RawMetadata ?? new Dictionary<string, object>()
            };

            // Add PhotosPicker creation time to raw metadata if available
            if (job.OriginalCreationTime.HasValue)
            {
                originalMetadata.RawMetadata["photospicker_creation_time"] = job.OriginalCreationTime.Value.ToString("yyyy-MM-ddTHH:mm:ss.fffZ");
                _logger.LogInformation("Using PhotosPicker creation time for job {JobId}: {CreationTime}", job.Id, job.OriginalCreationTime.Value);
            }

            job.OriginalMetadata = JsonSerializer.Serialize(originalMetadata);
            await compressionJobRepository.UpdateAsync(job);

            _logger.LogInformation("Successfully prepared video metadata for job {JobId}: Duration={Duration}, CreationTime={CreationTime} (PhotosPicker: {PhotosPickerTime}), Location={Location}, RawMetadataCount={RawMetadataCount}",
                job.Id, originalMetadata.Duration, originalMetadata.CreationTime, job.OriginalCreationTime, originalMetadata.Location, originalMetadata.RawMetadata?.Count ?? 0);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to prepare video metadata for job {JobId}, continuing without metadata", job.Id);
        }

        // Step 2: Upload to Cloud Storage
        await UpdateJobStatus(compressionJobRepository, job, CompressionJobStatus.UploadingToStorage);

        videoStream.Position = 0; // Reset stream for upload
        var inputFileName = $"{job.MediaItemId}_{DateTime.UtcNow:yyyyMMdd_HHmmss}.mp4";
        var inputPath = await storageService.UploadToInputBucketAsync(videoStream, inputFileName);
        job.InputStoragePath = inputPath;

        // Step 3: Start transcoding
        await UpdateJobStatus(compressionJobRepository, job, CompressionJobStatus.TranscodingInProgress);

        var expectedOutputPath = storageService.GenerateOutputPath(inputPath, job.Quality);

        (string transcoderJobName, string actualOutputPath) = await transcoderService.CreateTranscodingJobAsync(inputPath, expectedOutputPath, job.Quality, job.OriginalWidth, job.OriginalHeight);
        job.TranscoderJobName = transcoderJobName;
        job.OutputStoragePath = actualOutputPath;
        job.StartedAt = DateTime.UtcNow;

        await compressionJobRepository.UpdateAsync(job);

        _logger.LogInformation("Started transcoding job {TranscoderJobName} for compression job {JobId}",
            transcoderJobName, job.Id);
    }

    private async Task ProcessPhotoCompressionAsync(
        ICompressionJobRepository compressionJobRepository,
        CompressionJob job,
        GoogleCloudStorageService storageService,
        ImageCompressionService imageCompressionService,
        GooglePhotosService googlePhotosService,
        MediaMetadataService metadataService,
        string accessToken,
        CancellationToken cancellationToken)
    {
        // Step 1: Download photo from Google Photos
        await UpdateJobStatus(compressionJobRepository, job, CompressionJobStatus.DownloadingFromGooglePhotos);

        using var photoStream = await googlePhotosService.DownloadPhotoAsync(accessToken, job.MediaItemId, job.BaseUrl);
        var originalSize = photoStream.Length;
        job.OriginalSizeBytes = originalSize;

        // Step 1.5: Extract and store metadata from original photo
        photoStream.Position = 0;
        var originalMetadata = await metadataService.ExtractPhotoMetadataAsync(photoStream);

        // Use filename from job if available, otherwise use extracted filename
        if (!string.IsNullOrEmpty(job.OriginalFilename))
        {
            originalMetadata.OriginalFilename = job.OriginalFilename;
        }

        job.OriginalMetadata = JsonSerializer.Serialize(originalMetadata);
        await compressionJobRepository.UpdateAsync(job);

        // Step 2: Compress image directly (no cloud storage needed for photos)
        await UpdateJobStatus(compressionJobRepository, job, CompressionJobStatus.CompressingImage);

        using var compressedStream = new MemoryStream();
        photoStream.Position = 0;
        var compressionResult = await imageCompressionService.CompressImageAsync(
            photoStream, compressedStream, job.Quality, job.OriginalWidth, job.OriginalHeight);

        // Step 2.5: Apply metadata to compressed photo
        using var finalStream = new MemoryStream();
        if (!string.IsNullOrEmpty(job.OriginalMetadata))
        {
            try
            {
                var storedMetadata = JsonSerializer.Deserialize<PhotoMetadataInfo>(job.OriginalMetadata);
                if (storedMetadata != null)
                {
                    compressedStream.Position = 0;
                    await metadataService.ApplyPhotoMetadataAsync(compressedStream, storedMetadata, finalStream);
                    _logger.LogInformation("Applied metadata to compressed photo for job {JobId}", job.Id);
                }
                else
                {
                    compressedStream.Position = 0;
                    await compressedStream.CopyToAsync(finalStream, cancellationToken);
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to apply metadata to compressed photo for job {JobId}, using photo without metadata", job.Id);
                compressedStream.Position = 0;
                await compressedStream.CopyToAsync(finalStream, cancellationToken);
            }
        }
        else
        {
            compressedStream.Position = 0;
            await compressedStream.CopyToAsync(finalStream, cancellationToken);
        }

        job.CompressedSizeBytes = finalStream.Length;
        job.CompressionRatio = (double)job.CompressedSizeBytes / job.OriginalSizeBytes;
        job.StartedAt = DateTime.UtcNow;

        // Step 3: Upload compressed photo to cloud storage and save for batch upload if requested

        // Always upload compressed photo to cloud storage for downloads
        finalStream.Position = 0;
        var extension = job.Quality.ToLower() == "low" ? ".webp" : ".jpg";
        var outputFileName = $"{job.MediaItemId}_{DateTime.UtcNow:yyyyMMdd_HHmmss}_{job.Quality}{extension}";
        var outputPath = await storageService.UploadToOutputBucketAsync(finalStream, outputFileName);
        job.OutputStoragePath = outputPath;
        _logger.LogInformation("Uploaded compressed photo to cloud storage: {OutputPath}", outputPath);

        if (job.UploadToGooglePhotos)
        {
            // Also save compressed photo to temp file for batch upload
            var tempDir = Path.Combine(Path.GetTempPath(), "vidcompressor_batch");
            Directory.CreateDirectory(tempDir);

            var tempFilePath = Path.Combine(tempDir, $"{job.Id}_{DateTime.UtcNow:yyyyMMddHHmmss}{extension}");

            finalStream.Position = 0;
            await using (var fileStream = new FileStream(tempFilePath, FileMode.Create))
            {
                await finalStream.CopyToAsync(fileStream, cancellationToken);
            }

            job.CompressedFilePath = tempFilePath;
            await UpdateJobStatus(compressionJobRepository, job, CompressionJobStatus.ReadyForBatchUpload);

            _logger.LogInformation("Photo compression completed for job {JobId}, uploaded to {OutputPath} and saved to {FilePath}, ready for batch upload",
                job.Id, outputPath, tempFilePath);
        }
        else
        {
            // If not uploading to Google Photos, mark as completed
            await UpdateJobStatus(compressionJobRepository, job, CompressionJobStatus.Completed);
            job.CompletedAt = DateTime.UtcNow;

            _logger.LogInformation("Photo compression completed for job {JobId}, uploaded to {OutputPath}",
                job.Id, outputPath);
        }
        await compressionJobRepository.UpdateAsync(job);

        _logger.LogInformation("Completed photo compression job {JobId}. Original: {OriginalSize} bytes, Compressed: {CompressedSize} bytes, Ratio: {Ratio:P2}",
            job.Id, originalSize, job.CompressedSizeBytes, job.CompressionRatio);
    }

    private async Task MonitorTranscoderJobsAsync(CancellationToken cancellationToken)
    {
        using var scope = _serviceProvider.CreateScope();
        var compressionJobRepository = scope.ServiceProvider.GetRequiredService<ICompressionJobRepository>();
        var transcoderService = scope.ServiceProvider.GetRequiredService<GoogleTranscoderService>();

        var runningJobs = await compressionJobRepository.GetByStatusAsync(CompressionJobStatus.TranscodingInProgress);

        foreach (var job in runningJobs)
        {
            try
            {
                var status = await transcoderService.GetJobStatusAsync(job.TranscoderJobName!);

                if (status == TranscodingJobStatus.Succeeded)
                {
                    _logger.LogInformation("Transcoding completed for job {JobId}", job.Id);
                    await CompleteTranscodingAsync(job.Id, cancellationToken);
                }
                else if (status == TranscodingJobStatus.Failed)
                {
                    _logger.LogError("Transcoding failed for job {JobId}", job.Id);
                    var jobInfo = await transcoderService.GetJobInfoAsync(job.TranscoderJobName!);
                    await MarkJobAsFailed(job.Id, jobInfo.ErrorMessage ?? "Transcoding failed");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to check transcoder job status for {JobId}", job.Id);
            }
        }
    }

    private async Task CompleteTranscodingAsync(string jobId, CancellationToken cancellationToken)
    {
        using var scope = _serviceProvider.CreateScope();
        var compressionJobRepository = scope.ServiceProvider.GetRequiredService<ICompressionJobRepository>();
        var storageService = scope.ServiceProvider.GetRequiredService<GoogleCloudStorageService>();
        var googlePhotosService = scope.ServiceProvider.GetRequiredService<GooglePhotosService>();
        var metadataService = scope.ServiceProvider.GetRequiredService<MediaMetadataService>();

        var job = await compressionJobRepository.GetByIdAsync(jobId);
        if (job == null) return;

        try
        {
            // Step 4: Verify file exists and download compressed video from storage
            await UpdateJobStatus(compressionJobRepository, job, CompressionJobStatus.DownloadingFromStorage);

            // Check if the output file exists before attempting download
            var fileExists = await storageService.FileExistsAsync(job.OutputStoragePath!);
            if (!fileExists)
            {
                throw new InvalidOperationException($"Output file not found at expected path: {job.OutputStoragePath}");
            }

            _logger.LogInformation("Downloading compressed video from: {OutputPath}", job.OutputStoragePath);
            using var compressedVideoStream = await storageService.DownloadFromOutputBucketAsync(job.OutputStoragePath!);
            var compressedSize = compressedVideoStream.Length;
            job.CompressedSizeBytes = compressedSize;
            job.CompressionRatio = job.OriginalSizeBytes > 0 ? (double)compressedSize / job.OriginalSizeBytes : 0;

            // Step 4.5: Apply metadata to compressed video (similar to photo workflow)
            Stream finalVideoStream = compressedVideoStream;
            if (!string.IsNullOrEmpty(job.OriginalMetadata))
            {
                try
                {
                    var storedMetadata = JsonSerializer.Deserialize<VideoMetadataInfo>(job.OriginalMetadata);
                    if (storedMetadata != null)
                    {
                        _logger.LogInformation("Applying metadata to compressed video for job {JobId}: CreationTime={CreationTime}, Location={Location}, RawMetadataCount={RawMetadataCount}",
                            job.Id, storedMetadata.CreationTime, storedMetadata.Location, storedMetadata.RawMetadata?.Count ?? 0);

                        // Create temporary files for metadata application
                        var tempDir = Path.Combine(Path.GetTempPath(), "vidcompressor_metadata");
                        Directory.CreateDirectory(tempDir);

                        var tempInputPath = Path.Combine(tempDir, $"{job.Id}_input_{DateTime.UtcNow:yyyyMMddHHmmss}.mp4");
                        var tempOutputPath = Path.Combine(tempDir, $"{job.Id}_output_{DateTime.UtcNow:yyyyMMddHHmmss}.mp4");

                        try
                        {
                            // Save compressed video to temp file
                            compressedVideoStream.Position = 0;
                            await using (var tempFileStream = new FileStream(tempInputPath, FileMode.Create))
                            {
                                await compressedVideoStream.CopyToAsync(tempFileStream, cancellationToken);
                            }

                            // Apply metadata
                            await metadataService.ApplyVideoMetadataAsync(tempInputPath, storedMetadata, tempOutputPath);

                            // Read the metadata-enhanced video back into a stream
                            var metadataEnhancedStream = new MemoryStream();
                            await using (var tempFileStream = new FileStream(tempOutputPath, FileMode.Open))
                            {
                                await tempFileStream.CopyToAsync(metadataEnhancedStream, cancellationToken);
                            }

                            finalVideoStream = metadataEnhancedStream;

                            // Update compressed size in case metadata application changed it
                            job.CompressedSizeBytes = finalVideoStream.Length;
                            job.CompressionRatio = job.OriginalSizeBytes > 0 ? (double)job.CompressedSizeBytes / job.OriginalSizeBytes : 0;

                            _logger.LogInformation("Successfully applied metadata to compressed video for job {JobId}", job.Id);
                        }
                        finally
                        {
                            // Clean up temporary files
                            try
                            {
                                if (File.Exists(tempInputPath)) File.Delete(tempInputPath);
                                if (File.Exists(tempOutputPath)) File.Delete(tempOutputPath);
                            }
                            catch (Exception cleanupEx)
                            {
                                _logger.LogWarning(cleanupEx, "Failed to clean up temporary metadata files for job {JobId}", job.Id);
                            }
                        }
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Failed to apply metadata to compressed video for job {JobId}, continuing without metadata", job.Id);
                    // Continue with original compressed video if metadata application fails
                    finalVideoStream = compressedVideoStream;
                }
            }

            // Step 4.6: Re-upload the metadata-enhanced video to cloud storage
            finalVideoStream.Position = 0;
            var outputFileName = $"{job.MediaItemId}_{DateTime.UtcNow:yyyyMMdd_HHmmss}_{job.Quality}.mp4";
            var newOutputPath = await storageService.UploadToOutputBucketAsync(finalVideoStream, outputFileName);
            job.OutputStoragePath = newOutputPath;
            _logger.LogInformation("Re-uploaded metadata-enhanced video to cloud storage: {OutputPath}", newOutputPath);

            // Step 5: Save compressed video for batch upload if requested
            if (job.UploadToGooglePhotos)
            {
                // Save compressed video to persistent temp file for batch upload
                var tempDir = Path.Combine(Path.GetTempPath(), "vidcompressor_batch");
                Directory.CreateDirectory(tempDir);

                var tempFilePath = Path.Combine(tempDir, $"{job.Id}_{DateTime.UtcNow:yyyyMMddHHmmss}.mp4");

                finalVideoStream.Position = 0;
                await using (var fileStream = new FileStream(tempFilePath, FileMode.Create))
                {
                    await finalVideoStream.CopyToAsync(fileStream, cancellationToken);
                }

                job.CompressedFilePath = tempFilePath;
                await UpdateJobStatus(compressionJobRepository, job, CompressionJobStatus.ReadyForBatchUpload);

                _logger.LogInformation("Video compression completed for job {JobId}, saved to {FilePath}, ready for batch upload. " +
                    "Original size: {OriginalSize} bytes, Compressed size: {CompressedSize} bytes, " +
                    "Compression ratio: {CompressionRatio:P2}",
                    jobId, job.OriginalSizeBytes, job.CompressedSizeBytes, job.CompressionRatio, tempFilePath);
            }
            else
            {
                // If not uploading to Google Photos, mark as completed
                await UpdateJobStatus(compressionJobRepository, job, CompressionJobStatus.Completed);
                job.CompletedAt = DateTime.UtcNow;

                _logger.LogInformation("Video compression completed for job {JobId} (no upload requested). " +
                    "Original size: {OriginalSize} bytes, Compressed size: {CompressedSize} bytes, " +
                    "Compression ratio: {CompressionRatio:P2}",
                    jobId, job.OriginalSizeBytes, job.CompressedSizeBytes, job.CompressionRatio);
            }

            // Dispose the final stream if it's different from the original
            if (finalVideoStream != compressedVideoStream)
            {
                await finalVideoStream.DisposeAsync();
            }

            // Clean up input storage files only (keep output for downloads)
            await CleanupInputStorageFiles(storageService, job);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to complete compression job: {JobId}", jobId);
            await MarkJobAsFailed(jobId, ex.Message);
        }
    }

    private async Task UpdateJobStatus(ICompressionJobRepository compressionJobRepository, CompressionJob job, CompressionJobStatus status)
    {
        job.Status = status;
        await compressionJobRepository.UpdateAsync(job);
        _logger.LogInformation("Updated job {JobId} status to {Status}", job.Id, status);
    }

    private static string GetStatusMessage(CompressionJobStatus status)
    {
        var message = status switch
        {
            CompressionJobStatus.Queued => "Queued for processing",
            CompressionJobStatus.DownloadingFromGooglePhotos => "Downloading from Google Photos",
            CompressionJobStatus.UploadingToStorage => "Uploading to cloud storage",
            CompressionJobStatus.TranscodingInProgress => "Compressing video",
            CompressionJobStatus.CompressingImage => "Compressing photo",
            CompressionJobStatus.DownloadingFromStorage => "Downloading compressed media",
            CompressionJobStatus.ReadyForBatchUpload => "Ready for upload",
            CompressionJobStatus.UploadingToGooglePhotos => "Uploading to Google Photos",
            CompressionJobStatus.Completed => "Compression completed",
            CompressionJobStatus.Failed => "Compression failed",
            CompressionJobStatus.Cancelled => "Compression cancelled",
            _ => "Processing"
        };

        if (message == "Processing")
        {
            Console.WriteLine($"[WARNING] Unknown status {status} encountered, using 'Processing' message");
        }

        return message;
    }

    private static int GetProgressPercentage(CompressionJobStatus status, MediaType mediaType)
    {
        // Different progress flows for photos vs videos
        if (mediaType == MediaType.Photo)
        {
            return status switch
            {
                CompressionJobStatus.Queued => 0,
                CompressionJobStatus.DownloadingFromGooglePhotos => 20,
                // These shouldn't happen for photos, but if they do, maintain reasonable progress
                CompressionJobStatus.UploadingToStorage => 30,
                CompressionJobStatus.CompressingImage => 50,
                CompressionJobStatus.DownloadingFromStorage => 60,
                CompressionJobStatus.ReadyForBatchUpload => 70,
                CompressionJobStatus.UploadingToGooglePhotos => 90,
                CompressionJobStatus.Completed => 100,
                CompressionJobStatus.Failed => 0,
                CompressionJobStatus.Cancelled => 0,
                // For any unrecognized status, return 50% to avoid going backward
                _ => 50
            };
        }
        else // Video
        {
            return status switch
            {
                CompressionJobStatus.Queued => 0,
                CompressionJobStatus.DownloadingFromGooglePhotos => 10,
                CompressionJobStatus.UploadingToStorage => 20,
                CompressionJobStatus.TranscodingInProgress => 40,
                CompressionJobStatus.CompressingImage => 40, // Shouldn't happen for videos, but just in case
                CompressionJobStatus.DownloadingFromStorage => 60,
                CompressionJobStatus.ReadyForBatchUpload => 70,
                CompressionJobStatus.UploadingToGooglePhotos => 90,
                CompressionJobStatus.Completed => 100,
                CompressionJobStatus.Failed => 0,
                CompressionJobStatus.Cancelled => 0,
                // For any unrecognized status, return 50% to avoid going backward
                _ => 50
            };
        }
    }

    private async Task MarkJobAsFailed(string jobId, string errorMessage)
    {
        using var scope = _serviceProvider.CreateScope();
        var compressionJobRepository = scope.ServiceProvider.GetRequiredService<ICompressionJobRepository>();
        var creditsService = scope.ServiceProvider.GetRequiredService<CreditsService>();

        var job = await compressionJobRepository.GetByIdAsync(jobId);
        if (job != null)
        {
            job.Status = CompressionJobStatus.Failed;
            job.ErrorMessage = errorMessage;
            job.CompletedAt = DateTime.UtcNow;
            await compressionJobRepository.UpdateAsync(job);

            // Refund credits if they were deducted for this job
            if (job.CreditsUsed.HasValue && job.CreditsUsed.Value > 0)
            {
                var mediaTypeText = job.MediaType == MediaType.Photo ? "photo" : "video";
                var refundSuccess = await creditsService.RefundCreditsAsync(
                    job.UserId,
                    job.CreditsUsed.Value,
                    $"Refund for failed {mediaTypeText} compression ({job.Quality} quality)",
                    job.Id);

                if (refundSuccess)
                {
                    _logger.LogInformation("Refunded {Credits} credits to user {UserId} for failed job {JobId}",
                        job.CreditsUsed.Value, job.UserId, job.Id);
                }
                else
                {
                    _logger.LogError("Failed to refund {Credits} credits to user {UserId} for failed job {JobId}",
                        job.CreditsUsed.Value, job.UserId, job.Id);
                }
            }

            // Send failure notification
            _logger.LogInformation("Sending failure notification for job {JobId} to user {UserId}",
                job.Id, job.UserId);
        }
    }

    private async Task<string?> GetUserAccessToken(IUserRepository userRepository, string userId)
    {
        var user = await userRepository.GetByIdAsync(userId);
        if (user == null) return null;

        // Check if token is expired and refresh if needed
        if (user.GoogleTokenExpiry <= DateTime.UtcNow && !string.IsNullOrEmpty(user.GoogleRefreshToken))
        {
            // Token refresh logic would go here
            // For now, return the existing token
        }

        return user.GoogleAccessToken;
    }

    private async Task CleanupInputStorageFiles(GoogleCloudStorageService storageService, CompressionJob job)
    {
        try
        {
            // Only clean up input files - keep output files for downloads
            if (!string.IsNullOrEmpty(job.InputStoragePath))
            {
                await storageService.DeleteFileAsync(job.InputStoragePath);
                _logger.LogInformation("Cleaned up input storage file for job {JobId}: {InputPath}", job.Id, job.InputStoragePath);
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to cleanup input storage files for job {JobId}", job.Id);
        }
    }

    private async Task CleanupStorageFiles(GoogleCloudStorageService storageService, CompressionJob job)
    {
        try
        {
            if (!string.IsNullOrEmpty(job.InputStoragePath))
            {
                await storageService.DeleteFileAsync(job.InputStoragePath);
            }
            if (!string.IsNullOrEmpty(job.OutputStoragePath))
            {
                await storageService.DeleteFileAsync(job.OutputStoragePath);
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to cleanup storage files for job {JobId}", job.Id);
        }
    }

    private async Task CachePreviewImageAsync(
        ICompressionJobRepository compressionJobRepository,
        CompressionJob job,
        PreviewCacheService previewCacheService,
        string accessToken)
    {
        try
        {
            _logger.LogInformation("Caching preview image for job {JobId}", job.Id);

            // Cache the preview image (60x60 for job display)
            var previewPath = await previewCacheService.CachePreviewImageAsync(
                accessToken,
                job.MediaItemId,
                job.BaseUrl,
                width: 60,
                height: 60,
                crop: true);

            if (!string.IsNullOrEmpty(previewPath))
            {
                job.PreviewImagePath = previewPath;
                await compressionJobRepository.UpdateAsync(job);
                _logger.LogInformation("Successfully cached preview for job {JobId} at {PreviewPath}", job.Id, previewPath);
            }
            else
            {
                _logger.LogWarning("Failed to cache preview for job {JobId}", job.Id);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error caching preview for job {JobId}", job.Id);
            // Don't fail the job if preview caching fails - it's not critical
        }
    }
}
